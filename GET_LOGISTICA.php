<?php
// Aquí obtén la conexión a la base de datos y realiza la consulta
// ...
include("con_db.php"); // Asegúrate de que este archivo establece la conexión y define $conex

///// TODOS LOS RESPONSES QUE TIENE EL PERFIL DE TECNICO
//// Y SUPERVISOR
if (isset($_GET['id_usuario'])) {
    $id_usuario = $_GET['id_usuario'];
}

// Manejar proceso tanto de GET como POST
$proceso = '';
if (isset($_GET['proceso'])) {
    $proceso = $_GET['proceso'];
} elseif (isset($_POST['proceso'])) {
    $proceso = $_POST['proceso'];
} elseif (isset($_POST['accion'])) {
    $proceso = $_POST['accion']; // Para el caso de instalación que envía 'accion'
}

if (isset($_GET['serie'])) {
    $serie_hist = $_GET['serie'];
    // Proceder con la lógica cuando la variable está establecida
}

// Manejar endpoint de historial
if ($proceso === 'historial2' && isset($serie_hist)) {
    // Configurar headers para JSON solo en este endpoint
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    
    try {
        // Consulta para obtener el historial de movimientos de una serie específica
        $sql = "SELECT 
            tlm.id,
            tlm.fecha_hora,
            tlm.serie,
            tlm.id_tecnico_origen,
            tlm.id_tecnico_destino,
            tlm.observacion,
            tlm.id_movimiento,
            tlm.motivo,
            tlm.ticket,
            tplm.tipo_movimiento,
            tplm.Semantica,
            origen.nombre as Nombre_origen,
            origen.Nombre_short as Nombre_origen_short,
            destino.nombre as Nombre_destino,
            destino.Nombre_short as Nombre_destino_short,
            escaladas.archivo as archivo_adj
        FROM TB_LOGIS_MOVIMIENTOS tlm
        LEFT JOIN TP_LOGIS_MOVIMIENTOS tplm ON tlm.id_movimiento = tplm.id
        LEFT JOIN tb_user_tqw origen ON tlm.id_tecnico_origen = origen.id
        LEFT JOIN tb_user_tqw destino ON tlm.id_tecnico_destino = destino.id
        LEFT JOIN tb_logis_rut_orden_form escaladas ON escaladas.id_mov = tlm.id
        WHERE tlm.serie = ?
        ORDER BY tlm.fecha_hora DESC";
        
        $stmt = mysqli_prepare($conex, $sql);
        if (!$stmt) {
            throw new Exception('Error preparando consulta: ' . mysqli_error($conex));
        }
        
        mysqli_stmt_bind_param($stmt, 's', $serie_hist);
        mysqli_stmt_execute($stmt);
        $resultado = mysqli_stmt_get_result($stmt);
        
        if (!$resultado) {
            throw new Exception('Error ejecutando consulta: ' . mysqli_error($conex));
        }
        
        $historial = [];
        while ($fila = mysqli_fetch_assoc($resultado)) {
            $historial[] = $fila;
        }
        
        mysqli_stmt_close($stmt);
        
        // Log para debugging
        error_log("Historial obtenido para serie $serie_hist: " . count($historial) . " registros");
        
        // Respuesta exitosa en formato JSON
        echo json_encode($historial);
        exit;
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'error' => 'Error del servidor: ' . $e->getMessage(),
            'serie' => $serie_hist
        ]);
        exit;
    }
}

// Manejar endpoint para obtener precio de equipo
if ($proceso === 'precio' && isset($serie_hist)) {
    // Configurar headers para JSON solo en este endpoint
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    
    try {
        // Consulta para obtener el precio del equipo por serie
        $sql = "SELECT 
            tpve.monto_valor,
            tfd.descripcion,
            tfd.familia
        FROM tb_ferret_directa1 tfd1
        LEFT JOIN tp_ferret_desc tfd ON tfd.ID = tfd1.Item
        LEFT JOIN TP_LOGIST_VALORES_EQUIPO tpve ON tpve.descripcion_modelo_equipo = tfd.descripcion
        WHERE tfd1.Serial = ?
        LIMIT 1";
        
        $stmt = mysqli_prepare($conex, $sql);
        if (!$stmt) {
            throw new Exception('Error preparando consulta: ' . mysqli_error($conex));
        }
        
        mysqli_stmt_bind_param($stmt, 's', $serie_hist);
        mysqli_stmt_execute($stmt);
        $resultado = mysqli_stmt_get_result($stmt);
        
        if (!$resultado) {
            throw new Exception('Error ejecutando consulta: ' . mysqli_error($conex));
        }
        
        $precio_data = mysqli_fetch_assoc($resultado);
        mysqli_stmt_close($stmt);
        
        // Si no se encuentra en directa, buscar en reversa
        if (!$precio_data) {
            $sql_reversa = "SELECT 
                tpve.monto_valor,
                tfd.descripcion,
                tfd.familia
            FROM TB_LOGIST_bdReversa tbr
            LEFT JOIN tp_ferret_desc tfd ON tfd.ID = tbr.Item
            LEFT JOIN TP_LOGIST_VALORES_EQUIPO tpve ON tpve.descripcion_modelo_equipo = tfd.descripcion
            WHERE tbr.Serial = ?
            LIMIT 1";
            
            $stmt = mysqli_prepare($conex, $sql_reversa);
            if (!$stmt) {
                throw new Exception('Error preparando consulta reversa: ' . mysqli_error($conex));
            }
            
            mysqli_stmt_bind_param($stmt, 's', $serie_hist);
            mysqli_stmt_execute($stmt);
            $resultado = mysqli_stmt_get_result($stmt);
            $precio_data = mysqli_fetch_assoc($resultado);
            mysqli_stmt_close($stmt);
        }
        
        $precio = $precio_data ? $precio_data['monto_valor'] : 0;
        $descripcion = $precio_data ? $precio_data['descripcion'] : 'No disponible';
        
        // Log para debugging
        error_log("Precio obtenido para serie $serie_hist: $precio");
        
        // Respuesta exitosa en formato JSON
        echo json_encode([
            'precio' => $precio,
            'descripcion' => $descripcion,
            'serie' => $serie_hist
        ]);
        exit;
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'error' => 'Error del servidor: ' . $e->getMessage(),
            'serie' => $serie_hist
        ]);
        exit;
    }
}

if ($proceso === 'asignacion') {
    $asignacion = $conex->query(
        "   
     SELECT RZ.Serial  
    , RZ.Item  
    , RZ.Org  
    , RZ.Subinventory 
    , RZ.`Locator`  
    , RZ.State 
    , tlm.tipo_movimiento 
    , RZ.Status
    , RZ.`Receipt Date`  
           id_tecnico_origen
           , '' id_tecnico_destino
           , '' observacion
           , `Unit Number` as  id_movimiento
           , '' motivo
           , '' ticket
           ,tfd.descripcion  
           , tfd.familia  
           , '' as fechax 
           ,  CASE 
            WHEN Job is null THEN 0 
            WHEN Job = '' THEN 0
            ELSE Job 
        END as Job
           , Semantica    
           , Flag_trans     
               , tpve.monto_valor 
    FROM (SELECT Serial, Item, Org, Revision
    			, CASE 
    					WHEN Y.serie is not null THEN tut2.rut 
    					else Subinventory
    				END as Subinventory
    			, `Locator`
  				 	, Operation, Job, Step, Lot, State, Status, `Receipt Date`
  					, `Ship Date`, `Supplier Name`, `Supplier Lot`
  					, `Supplier Serial`, `Unit Number`, `Attributes`, `[  ]`
  					, `Unnamed: 20`, fecha_carga 
  					, tut2.rut 
                      , CASE 
                      WHEN Y.serie is not null THEN 'Si' 
                      else 'No'
                  END as Flag_trans
  					FROM TB_FERRET_DIRECTA1
				 AS DIRECTA
      LEFT JOIN 
      (SELECT serie, id_destino,rn
        FROM (
        SELECT serie , id_destino , row_number() OVER (PARTITION BY serie ORDER BY fecha DESC) rn
        FROM TB_LOGIS_TECNICO_SERIE_TRANSFIERE
        ) x WHERE rn = 1
      ) Y  
      ON DIRECTA.Serial = Y.serie
      LEFT JOIN tb_user_tqw tut2 ON tut2.id  = Y.id_destino    
    ) RZ        
    LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm  
    ON RZ.Attributes = tlm.id  
    LEFT JOIN       tb_user_tqw tut      ON         tut.rut = RZ.Subinventory      
    LEFT JOIN tp_ferret_desc tfd  ON tfd.ID  = RZ.Item 
    LEFT JOIN TB_LOGIS_CIERRE_INVENTARIO cierre     ON cierre.serie = RZ.Serial
    LEFT JOIN TP_LOGIST_VALORES_EQUIPO tpve ON tpve.descripcion_modelo_equipo = tfd.descripcion 
   WHERE (cierre.serie IS NULL AND (
          RZ.Attributes IS NULL OR 
          RZ.Attributes IN (2, 0, 9, 15, 21)
      ))  
    AND tut.id  = " . $id_usuario . "      
    AND State <> 'Issued out of stores'
      "
    );



    // Genera el código HTML de la tabla actualizada
    echo '<tr><th></th><th>Serial</th><th>Familia</th><th>Estado</th><th>Historial</th><th>Acepta</th><th>Rechaza</th></tr>';

    while ($fila = mysqli_fetch_assoc($asignacion)) {
        // echo '<tr class="tabla-fila" onclick="actualizarRegistro(\'' . $fila['Serial'] . '\', \'' . $fila['Item']. '\', ' . $id_usuario . ')">';
        echo '<td style="font-size: 12px;">';
        if ($fila['Job'] == '0') {
            echo '<span style="display:inline-block; width:12px; height:14px; background-color:green; border-radius:50%;"></span>';
        } else {
            echo '<span style="display:inline-block; width:12px; height:14px; background-color:red; border-radius:50%;"></span>';
        }
        echo '</td>';
        echo '<td class="estado-cell">' . $fila['Serial'] . '</td>';
        echo '<td class="estado-cell">' . $fila['familia'] . '</td>';
        echo '<td class="button-cell">' . $fila['Semantica'] . '</td>';
        echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', ' . $id_usuario . ', \'VER\')"><i class="bi bi-eye-fill"></i></button></td>';
        echo '<td class="button-cell"><button type="button" class="btn btn-success" onclick="actualizarRegistro(\'' . $fila['Serial'] . '\', \'' . $fila['Flag_trans'] . '\' , \'' . $fila['id_movimiento'] . '\', \'ACEPTA\')"><i class="bi bi-check-circle"></i></button></td>';
        echo '<td class="button-cell"><button type="button" class="btn btn-danger" onclick="rechazoMaterial(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['Item'] . '\', \'RECHAZA\')"><i class="bi bi-journal-x"></i></button></td>';
        echo '</tr>';
    }
} 

elseif ($proceso === "reversa") {
    $bd_reversa = $conex->query("
     SELECT RZ.Serial  
    , RZ.Item  
    , RZ.Org  
    , RZ.Subinventory  
    , SUBSTRING(`Locator`  , 1, LENGTH(`Locator`) - 2) as `Locator`
    , RZ.State 
    , RZ.Status
    , tlm.tipo_movimiento 
    , RZ.`Receipt Date`  
           id_tecnico_origen                      
           , `Unit Number` AS id_movimiento           
           , tut.Nombre_short  
           , tfd.descripcion  
           , tfd.familia  
           , case 
           WHEN `Unit Number` is null then 'Pendiente por entregar'
           WHEN `Unit Number` = 0  then 'Pendiente por entregar'
                else  Semantica
           end as Semantica
             , tpve.monto_valor
    FROM 
        (
            SELECT Serial, Item, Org, Revision, Subinventory, `Locator`, Operation, Job
        , Step, Lot, State, Status, `Receipt Date`, `Ship Date`, `Supplier Name`
        , `Supplier Lot`, `Supplier Serial`, `Unit Number`, `Attributes`, `[  ]`, `Unnamed: 20` 
         FROM TB_LOGIST_bdReversa
        
        ) RZ    
    LEFT JOIN tb_logis_movimientos A ON A.id = RZ.`Unit Number`
    LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm      ON A.id_movimiento = tlm.id  
    LEFT JOIN tb_user_tqw tut   ON   SUBSTRING(tut.rut, 1, LENGTH(tut.rut) - 2)  = SUBSTRING(`Locator`, 1, LENGTH(`Locator`) - 2)      
    LEFT JOIN tp_ferret_desc tfd    ON RZ.Item  = tfd.ID  
    LEFT JOIN TB_LOGIS_CIERRE_INVENTARIO cierre     ON cierre.serie = RZ.Serial
    LEFT JOIN TP_LOGIST_VALORES_EQUIPO tpve ON tpve.descripcion_modelo_equipo = tfd.descripcion 
    WHERE CASE 
        WHEN cierre.serie  is not null THEN 'NO' 
        WHEN `Unit Number` = 12 THEN 'NO'
        WHEN `Unit Number` = 4 THEN 'NO'
         ELSE 'SI' 
    END = 'SI'
    AND State <> 'Issued out of stores'
    AND tut.id  = " . $id_usuario . "
    

     ");


    echo '<tr><th>Serial</th><th>Estado</th><th>Historial</th><th>Entregar</th><th>Escalamiento</th></tr>';

    while ($fila = mysqli_fetch_assoc($bd_reversa)) {
        echo '<tr>';

        echo '<td class="button-cell">' . $fila['Serial'] . '</td>';
        echo '<td class="button-cell">' . $fila['Semantica'] . '</td>';
        echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', ' . $id_usuario . ', \'VER\')"><i class="bi bi-eye-fill"></i></button></td>';
        echo '<td class="button-cell"><button type="button" class="btn btn-success" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'ENTREGA_REV\')"><i class="bi bi-house-check-fill"></i></button></td>';
        echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'TRANSFIERE_REV\')"><i class="bi bi-send-fill"></i></button></td>';
        echo '</tr>';
    }
}

////  TECNICO - DIRECTA
elseif ($proceso === "directa") {


    $directa = $conex->query(
        "   
        SELECT RZ.Serial  
        , RZ.Item          
                 ,Subinventory                                
                , RZ.`Attributes` as  id_movimiento                
                , tlm.tipo_movimiento 
                ,tfd.descripcion  , tfd.familia  
                , Semantica    
                , job          
                , tpve.monto_valor  
        FROM 
        (
        SELECT Serial, Item, Org, Revision
        , CASE 
                WHEN Y.serie is not null THEN tut2.rut 
                else Subinventory
            END as Subinventory
        , `Locator`
        , Operation 
        , CASE 
                WHEN job is null THEN 0 
                WHEN job = '' THEN 0
                ELSE job 
            END as job
        , Step, Lot, State, Status, `Receipt Date`
        , `Ship Date`, `Supplier Name`, `Supplier Lot`
        , `Supplier Serial`, `Unit Number`, `Attributes`, `[  ]`
        , `Unnamed: 20`, fecha_carga 
        , tut2.rut 
        FROM TB_FERRET_DIRECTA1
        AS DIRECTA
          LEFT JOIN  
		  (select * from 
			  (
			  SELECT
			  *, ROW_NUMBER() OVER (PARTITION BY serie ORDER BY fecha DESC) AS row_num_trans			 
			  	FROM TB_LOGIS_TECNICO_SERIE_TRANSFIERE 		  
			  ) YY 
		  		WHERE YY.row_num_trans = 1 		  
		  )Y 
		   ON DIRECTA.Serial  = Y.serie	
        LEFT JOIN tb_user_tqw tut2 ON tut2.id  = Y.id_destino    
        ) RZ        
        LEFT JOIN tb_logis_movimientos MOVIMI ON `MOVIMI`.id = RZ.`Unit Number`
        LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm          ON MOVIMI.id_movimiento = tlm.id  
        LEFT JOIN             tb_user_tqw tut          ON             tut.rut = RZ.Subinventory          
        LEFT JOIN tp_ferret_desc tfd          ON tfd.ID  = RZ.Item
        LEFT JOIN TP_LOGIST_VALORES_EQUIPO tpve ON tpve.descripcion_modelo_equipo = tfd.descripcion 
          LEFT JOIN TB_LOGIS_CIERRE_INVENTARIO cierre     ON cierre.serie = RZ.Serial           
        WHERE `MOVIMI`.id IS NOT NULL
        and CASE 
            WHEN cierre.serie  is not null THEN 'NO' 
            WHEN MOVIMI.id_movimiento = 2 THEN 'NO' 
            WHEN MOVIMI.id_movimiento = 9 THEN 'NO' 
            WHEN MOVIMI.id_movimiento = 15 THEN 'NO'
            WHEN MOVIMI.id_movimiento = 0 THEN 'NO'
            WHEN MOVIMI.id_movimiento = 21 THEN 'NO' 
            ELSE 'SI'
        END = 'SI'           	
        AND tut.id  = '" . $id_usuario . "'
            AND State <> 'Issued out of stores'
            "
    );



    // Genera el código HTML de la tabla actualizada
    echo '<tr><th></th><th>Serial</th><th>Familia</th><th>Estado</th><th>Historial</th><th>Declarar</th><th>Escalamiento</th></tr>';

    while ($fila = mysqli_fetch_assoc($directa)) {
        echo '<tr>';
        echo '<td style="font-size: 12px;">';
        if ($fila['job'] == '0') {
            echo '<span style="display:inline-block; width:12px; height:14px; background-color:green; border-radius:50%;"></span>';
        } else {
            echo '<span style="display:inline-block; width:12px; height:14px; background-color:red; border-radius:50%;"></span>';
        }
        echo '</td>';
        echo '<td class="estado-cell">' . $fila['Serial'] . '</td>';
        echo '<td class="estado-cell">' . $fila['familia'] . '</td>';
        echo '<td class="estado-cell" ' . strtolower($fila['Semantica']) . '">' . $fila['Semantica'] . '</td>';
        echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['monto_valor'] . '\', \'' . $fila['id_movimiento'] . '\', \'VER\')"><i class="bi bi-eye-fill"></i></button></td>';
        echo '<td class="estado-cell"><button type="button" class="btn btn-success" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'INSTALA\')"><i class="bi bi-house-check-fill"></i></button></td>';
        echo '<td class="estado-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'TRANSFIERE\')"><i class="bi bi-send-fill"></i></button></td>';
        echo '</tr>';
    }

    
} 

elseif ($proceso === "inventario") {
    $inventario = $conex->query(
        "SELECT                          
        tlci.Serial                
       , tlci.flujo 
       , CASE 
         WHEN Semantica IS NULL or A.id_movimiento = 0 THEN 
            CASE 
                WHEN flujo = 'Reversa' THEN 'Pendiente por entregar'
                WHEN flujo = 'Directa' THEN 'Entregado a técnico'
            END          
          ELSE Semantica
       END AS Semantica
       , A.id_movimiento
       , '' as item
       FROM TB_LOGIS_CIERRE_INVENTARIO2 tlci
        LEFT JOIN `TB_LOGIS_UploadCierreTecnico` `tluct` ON `tluct`.`serie` = `tlci`.`serial` AND `tluct`.`id` = `tlci`.`ID_CIERRE`
       LEFT JOIN tb_user_tqw tut      ON tut.Nombre_short  = tlci.id_tecnico 
       LEFT JOIN 
    (
        SELECT DISTINCT SERIES , id_cierre FROM 
    (
        SELECT SERIES , 'Fisico' as tipo_inventario , id_cierre
        FROM TB_LOGIS_INVENTARIO_FISICO 
        UNION ALL
        SELECT SERIES , 'Bodega' as tipo_inventario , id_cierre
        FROM TB_LOGIS_INVENTARIO_FISICO_BODEGA
        UNION ALL
        SELECT SERIES , 'Reversa' as tipo_inventario , id_cierre
        FROM TB_LOGIS_INVENTARIO_FISICO_reversa 
        UNION ALL
        SELECT SERIES , 'Ticket' as tipo_inventario , id_cierre
        FROM TB_LOGIS_INVENTARIO_FISICO_ticket                
    ) as A  
    )fisico       ON fisico.SERIES = tlci.serial        
    AND fisico.id_cierre = tlci.ID_CIERRE
       LEFT JOIN 
                   (
                   SELECT id, fecha_hora, serie,
                       id_tecnico_origen, id_tecnico_destino,
                       observacion, id_movimiento, motivo , ticket
                   FROM (
                       SELECT id, fecha_hora, serie,
                           id_tecnico_origen, id_tecnico_destino,ticket,
                           observacion, id_movimiento, motivo,
                           ROW_NUMBER() OVER (PARTITION BY serie ORDER BY fecha_hora DESC) AS row_num
                       FROM TB_LOGIS_MOVIMIENTOS
                   ) AS ranked
                   WHERE row_num = 1
                   ) A 
               ON A.serie = tlci.Serial  
        LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm      ON tlm.id  = A.id_movimiento        
        WHERE fisico.SERIES IS NULL   
        and tut.id = '$id_usuario'
        AND tluct.id IS NULL 
        AND CASE 
        	WHEN tlm.descuento = '1' THEN 'SI'	
	        WHEN tlm.descuento IS NULL THEN 'SI'
	        ELSE 'NO'
        END = 'SI'
        AND CASE 
            WHEN state = 'Issued out of stores' THEN 'NO'
            WHEN tlci.Geo_inv = 'VIÑA' and tlci.flujo = 'REVERSA' AND fecha_carga_reversa is null THEN 'NO'
            ELSE 'SI'
        END = 'SI'
       
        
        "
    );

    echo '<tr><th>Serial</th><th>Origen</th><th>Estado</th><th>VER</th><th>Declarar</th><th>Escalar</th></tr>';

    while ($fila = mysqli_fetch_assoc($inventario)) {

        echo '<td class="button-cell ajustado">' . $fila['Serial'] . '</td>';
        echo '<td class="button-cell">' . $fila['flujo'] . '</td>';
        echo '<td class="button-cell">' . $fila['Semantica'] . '</td>';
        echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Serial'] . '\', \'' . $fila['Serial'] . '\', \'VER\')"><i class="bi bi-eye-fill"></i></button></td>';
        if ($fila['flujo'] == 'Directa') {
            echo '<td class="button-cell"><button type="button" class="btn btn-success" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'INSTALA\')"><i class="bi bi-house-check-fill"></i></button></td>';
            echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'TRANSFIERE\')"><i class="bi bi-send-fill"></i></button></td>';
        } else {
            echo '<td class="button-cell"><button type="button" class="btn btn-success" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'ENTREGA_REV\')"><i class="bi bi-house-check-fill"></i></button></td>';
            echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'TRANSFIERE_REV\')"><i class="bi bi-send-fill"></i></button></td>';
        }

        echo '</tr>';
    }
} 

elseif ($proceso === "directa_super") {


    $directa_super = $conex->query(
        "   
        SELECT 
        RZ.Serial  
        , tut.Nombre_short   
        ,   fecha_hora
        , RZ.Item  
        FROM 
        TB_FERRET_DIRECTA1 RZ
        LEFT JOIN 
        (
        SELECT id, fecha_hora, serie,
        id_tecnico_origen, id_tecnico_destino,
        observacion, id_movimiento, motivo , ticket
        FROM (
        SELECT id, fecha_hora, serie,
        id_tecnico_origen, id_tecnico_destino,ticket,
        observacion, id_movimiento, motivo,
        ROW_NUMBER() OVER (PARTITION BY serie ORDER BY fecha_hora DESC) AS row_num
        FROM TB_LOGIS_MOVIMIENTOS
        ) AS ranked
        WHERE row_num = 1
        ) A 
        ON RZ.Serial = A.serie
        LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm  
        ON A.id_movimiento = tlm.id  
        LEFT JOIN 
        tb_user_tqw tut  
        ON 
        tut.rut = RZ.Subinventory  
        LEFT JOIN tb_user_tqw tut_destino  
        ON tut_destino.id = A.id_tecnico_destino
        LEFT JOIN tp_ferret_desc tfd  
        ON tfd.ID  = RZ.Item   
        WHERE A.id_movimiento IS NOT NULL
        and A.id_movimiento <> 2 
        AND tut_destino.id  = '" . $id_usuario . "'
        AND State <> 'Issued out of stores'
        "
        );



    // Genera el código HTML de la tabla actualizada
    echo '<tr><th>Serial</th><th>Tecnicox</th><th>Fecha</th><th>RECHAZA</th><th>TRANSFERIR</th><th>JUSTIFICAR</th></tr>';

    while ($fila = mysqli_fetch_assoc($directa_super)) {
        echo '<tr>';
        echo '<td>' . $fila['Serial'] . '</td>';
        echo '<td>' . $fila['Nombre_short'] . '</td>';
        echo '<td>' . $fila['fecha_hora'] . '</td>';
        echo '<td><button type="button" class="btn btn-danger" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', ' . $id_usuario . ', \'RECHAZA\')"><i class="bi bi-journal-x"></i></button></td>';
        echo '<td><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', ' . $id_usuario . ', \'TRANSFERIR\')"><i class="bi bi-person-bounding-box"></i></button></td>';
        echo '<td><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', ' . $id_usuario . ', \'JUSTIFICAR\')"><i class="bi bi-person-bounding-box"></i></button></td>';
        echo '</tr>';
    }
} 

elseif ($proceso === "historial") {



    $bd_historial = $conex->query(
        "   
        SELECT A.motivo  , A.serie , A.id_tecnico_origen  
        , A.id_tecnico_destino 
        , A.archivo_adj 
        , A.fecha_hora 
    , B.Nombre_short  as Nombre_origen
          , C.Nombre_short as Nombre_destino
          , tfd.familia
          , TP_MOV.Semantica , TP_MOV.movimiento_historial
          , A.observacion
        FROM
         TB_LOGIS_MOVIMIENTOS A 
        LEFT JOIN tb_user_tqw  B         ON A.id_tecnico_origen = B.id 
        LEFT JOIN tb_user_tqw C         ON A.id_tecnico_destino = C.id
        LEFT JOIN TB_FERRET_DIRECTA1 AS  BASE_DIRECTA        ON  BASE_DIRECTA.Serial = A.serie
          LEFT JOIN tp_ferret_desc tfd      ON tfd.ID  = BASE_DIRECTA.Item 
        LEFT JOIN TP_LOGIS_MOVIMIENTOS TP_MOV         ON TP_MOV.ID = A.id_movimiento 
                            
        WHERE serie ='" . $serie_hist . "'  
        ORDER BY fecha_hora  desc  
        LIMIT 20
          "
    );
    while ($fila = mysqli_fetch_assoc($bd_historial)) {
        // Parte 1: Círculo y línea
        echo '<div class="card-item">';  // Contenedor para cada tarjeta

        echo '<div class="card-content">';
        echo '<span class="card-date">Fecha: ' . $fila['fecha_hora'] . '</span>';
        echo '<p class="card-info">Movimiento: ' . $fila['Semantica'] . '</p>';
        echo '<p class="card-info">Desde: ' . $fila['Nombre_origen'] . '</p>';

        if (!empty($fila['movimiento_historial'])) {  // Comprueba si el campo 'archivo_adj' no está vacío
            echo '<p class="card-info">Hacia: ' . $fila['Nombre_destino'] . '</p>';
        } else {
            echo '<p class="card-info">Hacia: ' . $fila['Nombre_destino'] . '</p>';
        }


        if (!empty($fila['motivo'])) {  // Comprueba si el campo 'archivo_adj' no está vacío
            echo '<p class="card-info">Motivo: ' . $fila['motivo'] . '</p>';
        }

        if (!empty($fila['observacion'])) {  // Comprueba si el campo 'observacion' no está vacío
            // Obtiene los primeros 30 caracteres de la observación
            $textoCorto = substr($fila['observacion'], 0, 30);
            // Añade '...' si el texto original es más largo que el texto corto
            $textoMostrar = (strlen($fila['observacion']) > 30) ? $textoCorto . '...' : $textoCorto;

            echo '<p class="card-info" title="' . htmlspecialchars($fila['observacion'], ENT_QUOTES, 'UTF-8') . '">Observación: ' . $textoMostrar . '</p>';
        }


        if (!empty($fila['archivo_adj'])) {  // Comprueba si el campo 'archivo_adj' no está vacío
            echo '<p class="card-info"> <a href="' . $fila['archivo_adj'] . '" download>Descargar respaldo</a></p>';
        }
        // ... (otros campos)
        echo '</div>';

        echo '</div>';  // Fin del contenedor para cada tarjeta
    }
    echo '</div>';
}

elseif ($proceso === "historial2") {
    // Versión JSON del historial para uso con fetch API
    header('Content-Type: application/json');

    try {
        // Consulta para obtener el historial de movimientos de una serie específica
        $query = "SELECT 
            tlm.id,
            DATE_FORMAT(tlm.fecha_hora, '%d-%m-%Y %H:%i:%s') as fecha,
            tlm.serie,
            tlm.id_tecnico_origen,
            tlm.id_tecnico_destino,
            tlm.observacion,
            tlm.id_movimiento,
            tlm.motivo,
            tlm.ticket,
            tplm.tipo_movimiento as accion,
            tplm.Semantica as estado,
            origen.nombre as usuario_origen,
            origen.Nombre_short as usuario_origen_short,
            destino.nombre as usuario_destino,
            destino.Nombre_short as usuario_destino_short,
            escaladas.archivo as archivo_adj
        FROM TB_LOGIS_MOVIMIENTOS tlm
        LEFT JOIN TP_LOGIS_MOVIMIENTOS tplm ON tlm.id_movimiento = tplm.id
        LEFT JOIN tb_user_tqw origen ON tlm.id_tecnico_origen = origen.id
        LEFT JOIN tb_user_tqw destino ON tlm.id_tecnico_destino = destino.id
        LEFT JOIN tb_logis_rut_orden_form escaladas ON escaladas.id_mov = tlm.id
        WHERE tlm.serie = ?
        ORDER BY tlm.fecha_hora DESC";
        
        $stmt = mysqli_prepare($conex, $query);
        mysqli_stmt_bind_param($stmt, 's', $serie_hist);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        $data = [];
        while ($row = mysqli_fetch_assoc($result)) {
            // Formatear datos para mostrar en timeline
            $data[] = [
                'id' => $row['id'],
                'fecha' => $row['fecha'],
                'serie' => $row['serie'],
                'accion' => $row['accion'] ?: 'Movimiento',
                'estado' => $row['estado'] ?: 'Estado no definido',
                'motivo' => $row['motivo'],
                'observacion' => $row['observacion'],
                'usuario' => $row['usuario_origen_short'] ?: ($row['usuario_origen'] ?: 'Usuario no identificado'),
                'usuario_destino' => $row['usuario_destino_short'] ?: ($row['usuario_destino'] ?: ''),
                'archivo' => $row['archivo_adj']
            ];
        }
        
        // Respuesta JSON formateada
        echo json_encode([
            'success' => true,
            'data' => $data,
            'count' => count($data),
            'message' => count($data) > 0 ? 'Historial encontrado' : 'No se encontró historial para esta serie'
        ]);
    } catch (Exception $e) {
        // Error en formato JSON
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'message' => 'Error al consultar el historial'
        ]);
    }
    exit;
}

// Manejar proceso de instalación
elseif ($proceso === 'instalacion') {
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    
    try {
        // Obtener datos del POST
        $serie = $_POST['serie'] ?? '';
        $orden_trabajo = $_POST['orden_trabajo'] ?? '';
        $rut_cliente = $_POST['rut_cliente'] ?? '';
        $observaciones = $_POST['observaciones'] ?? '';
        
        // Validar datos requeridos
        if (empty($serie) || empty($orden_trabajo) || empty($observaciones)) {
            echo json_encode([
                'success' => false,
                'message' => 'Faltan datos requeridos: serie, orden de trabajo y observaciones son obligatorios'
            ]);
            exit;
        }
        
        // Log de debugging (temporal)
        error_log("Instalación - Serie: $serie, OT: $orden_trabajo, RUT: $rut_cliente, Obs: $observaciones");
        
        // Aquí iría la lógica real de inserción en base de datos
        // Por ahora retornamos éxito para testing
        
        // Respuesta de éxito
        echo json_encode([
            'success' => true,
            'message' => 'Instalación declarada correctamente',
            'data' => [
                'serie' => $serie,
                'orden_trabajo' => $orden_trabajo,
                'rut_cliente' => $rut_cliente,
                'observaciones' => $observaciones
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error al procesar la instalación: ' . $e->getMessage()
        ]);
    }
    exit;
}

// Endpoint para cargar lista de técnicos
elseif ($proceso === 'lista_tecnicos' || (isset($_GET['accion']) && $_GET['accion'] === 'lista_tecnicos')) {
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    
    try {
        $sql = "SELECT id, nombre, rut, Nombre_short
                FROM tb_user_tqw
                WHERE PERFIL IN ('TECNICO RESIDENCIAL', 'TECNICO REDES', 'Supervisor Tecnico')
                  AND vigente = 'Si'
                ORDER BY nombre";
        
        $result = mysqli_query($conex, $sql);
        if (!$result) {
            throw new Exception('Error en consulta: ' . mysqli_error($conex));
        }
        
        $tecnicos = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $tecnicos[] = [
                'id' => $row['id'],
                'nombre' => $row['nombre'],
                'rut' => $row['rut'],
                'nombre_short' => $row['Nombre_short']
            ];
        }
        
        echo json_encode([
            'success' => true,
            'tecnicos' => $tecnicos
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error al cargar técnicos: ' . $e->getMessage()
        ]);
    }
    exit;
}