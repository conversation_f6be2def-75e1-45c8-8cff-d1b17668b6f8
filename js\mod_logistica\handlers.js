/**
 * handlers.js
 * Manejadores de eventos para el módulo de logística
 * Extraído del bloque JavaScript monolítico en mod_logistica.php
 */

// Función para cargar lista de técnicos
function cargarTecnicos() {
    console.log('🔄 Cargando lista de técnicos...');
    
    const selectElement = document.getElementById('usuario_destino');
    if (!selectElement) {
        console.error('❌ No se encontró el elemento usuario_destino');
        return;
    }
    
    // Mostrar estado de carga
    selectElement.innerHTML = '<option value="">Cargando técnicos...</option>';
    
    const xhr = new XMLHttpRequest();
    const endpoint = window.ModLogisticaConfig.endpoints.tecnicos;
    
    xhr.open('GET', endpoint, true);
    xhr.onload = function() {
        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                
                if (response.success && response.tecnicos) {
                    // Limpiar el select
                    selectElement.innerHTML = '<option value="">Seleccione el técnico a transferir</option>';
                    
                    // Agregar opciones de técnicos
                    response.tecnicos.forEach(tecnico => {
                        const option = document.createElement('option');
                        option.value = tecnico.id;
                        option.textContent = `${tecnico.nombre} (${tecnico.rut})`;
                        selectElement.appendChild(option);
                    });
                    
                    console.log('✅ Técnicos cargados correctamente:', response.tecnicos.length);
                } else {
                    console.error('❌ Error en respuesta:', response.message || 'Respuesta inválida');
                    selectElement.innerHTML = '<option value="">Error al cargar técnicos</option>';
                }
            } catch (e) {
                console.error('❌ Error al parsear respuesta JSON:', e);
                selectElement.innerHTML = '<option value="">Error al cargar técnicos</option>';
            }
        } else {
            console.error('❌ Error en petición HTTP:', xhr.status);
            selectElement.innerHTML = '<option value="">Error al cargar técnicos</option>';
        }
    };
    
    xhr.onerror = function() {
        console.error('❌ Error de red al cargar técnicos');
        selectElement.innerHTML = '<option value="">Error de conexión</option>';
    };
    
    xhr.send();
}

// Función principal para redirigir en transferencia/instalación
function redirigirEnTransferencia(serial, item, ID_MOVI, accion) {
    console.log('🔄 redirigirEnTransferencia LLAMADA:', { serial, item, ID_MOVI, accion });
    
    // Validaciones comunes para INSTALA y TRANSFIERE
    function validarEstado() {
        if (ID_MOVI === '5') {
            console.log('⚠️ Estado 5: Ya declarado como instalado');
            mostrarNotificacion('Serial ya fue declarado como instalado', 'warning');
            return false;
        } else if (ID_MOVI === '3') {
            console.log('⚠️ Estado 3: Pendiente por el usuario');
            mostrarNotificacion('Aun pendiente por el usuario a quien escalaste el material', 'warning');
            return false;
        } else if (ID_MOVI === '4') {
            console.log('⚠️ Estado 4: Justificado por supervisor');
            mostrarNotificacion('El material ya fue justificado por el supervisor', 'warning');
            return false;
        } else if (ID_MOVI === '13') {
            console.log('⚠️ Estado 13: Escalado a VTR');
            mostrarNotificacion('El material ha sido escalado a VTR', 'warning');
            return false;
        }
        return true;
    }

    // Función auxiliar para preparar y mostrar offcanvas
    function prepararOffcanvas(targetId, inputId, serial) {
        try {
            // Asegurarnos de que el body esté en un estado consistente
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
            // Eliminar cualquier backdrop existente antes de crear uno nuevo
            const backdropElements = document.querySelectorAll('.offcanvas-backdrop');
            backdropElements.forEach(element => {
                element.remove();
            });
            
            // Cerrar cualquier otro offcanvas abierto
            document.querySelectorAll('.offcanvas.show').forEach(offcanvasEl => {
                const instance = bootstrap.Offcanvas.getInstance(offcanvasEl);
                if (instance) {
                    try {
                        instance.hide();
                    } catch (e) {
                        console.warn('Error al ocultar offcanvas:', e);
                    }
                }
            });
            
            // Asignar valor al campo de serie
            const serieInput = document.getElementById(inputId);
            if (serieInput) {
                serieInput.value = serial;
                console.log('🔍 Valor de ' + inputId + ' establecido:', serial);
            } else {
                console.error('No se encontró el campo ' + inputId);
            }
            
            const offcanvasElement = document.getElementById(targetId);
            console.log('🔍 Elemento offcanvas encontrado:', !!offcanvasElement);
            
            if (!offcanvasElement) {
                throw new Error('No se encontró el elemento offcanvas ' + targetId);
            }
            
            // Asegurarse de que tiene el atributo data-bs-backdrop="true"
            offcanvasElement.setAttribute('data-bs-backdrop', 'true');
            
            // Asegurarse de que el offcanvas no esté en estado show
            offcanvasElement.classList.remove('show');
            
            // Esperar un breve momento para asegurar que cualquier instancia previa se haya limpiado
            setTimeout(() => {
                try {
                    // Crear el objeto offcanvas con opciones explícitas
                    const offcanvas = new bootstrap.Offcanvas(offcanvasElement, {
                        backdrop: true,
                        scroll: false
                    });
                    
                    // Mostrar el offcanvas
                    offcanvas.show();
                    console.log('🔓 Offcanvas ' + targetId + ' abierto');
                    
                    // Si es el formulario de transferencia, cargar técnicos
                    if (targetId === 'offcanvasrigh') {
                        cargarTecnicos();
                    }
                } catch (innerError) {
                    console.error('Error al mostrar offcanvas:', innerError);
                    mostrarNotificacion('Ocurrió un error al mostrar el formulario', 'danger');
                }
            }, 50);
        } catch (error) {
            console.error('Error general al procesar acción:', error);
            mostrarNotificacion('Error al mostrar el formulario', 'danger');
        }
    }
    
    if (accion === 'VER') {
        console.log('👁️‍🗨️ Procesando historial');
        prepararOffcanvas('offcanvasHistorial', 'serieHistorial', serial);
        // Cargar el historial después de abrir el canvas
        if (typeof cargarHistorialDirecto === 'function') {
            cargarHistorialDirecto(serial, item).then(data => {
                // Aquí puedes procesar y mostrar la línea de tiempo si es necesario
            }).catch(err => console.error("Error al cargar datos del historial:", err));
        } else {
            console.error("cargarHistorialDirecto no está disponible");
        }
    } else if (accion === 'INSTALA') {
        console.log('🏠 Procesando instalación');
        if (!validarEstado()) return;
        prepararOffcanvas('offcanvasInstala', 'serie_insta', serial);
    } else if (accion === 'TRANSFIERE') {
        console.log('📤 Procesando transferencia');
        if (!validarEstado()) return;
        prepararOffcanvas('offcanvasrigh', 'serie_tran', serial);
    }
}

// Función para actualizar registro (aceptar/rechazar material)
function actualizarRegistro(Serial, ticket, id_tecnico, accion) {
    // Obtener el elemento del botón que disparó el evento
    const buttonElement = event.target.closest('button');
    
    // Crear FormData para enviar los datos en POST
    var formData = new FormData();
    
    if (accion === 'ENTREGA_REV') {
        if (id_tecnico === '6') {
            mostrarNotificacion('Bodega tiene pendiente la confirmación', 'warning');
        } else if (id_tecnico === '7') {
            mostrarNotificacion('Material pendiente de revisión por supervisor', 'warning');
        } else {
            // Lógica para entrega reversa
            formData.append('Serial', Serial);
            formData.append('accion', accion);
            formData.append('id_tecnico_origen', window.userId);
            formData.append('ticket', ticket);
            
            enviarSolicitudAceptacion(formData, buttonElement);
        }
    } else {
        if (accion === 'ACEPTA') {
            // ID_MOVIMIENTO - Validación específica
            if (id_tecnico == 2) {
                mostrarNotificacion("NO PODRÁS CONFIRMAR HASTA LA REVISIÓN DE BODEGA", 'warning');
            } else {
                // Preparar datos para aceptación
                formData.append('Serial', Serial);
                formData.append('accion', accion);
                formData.append('id_tecnico_origen', window.userId);
                formData.append('ticket', ticket);
                
                enviarSolicitudAceptacion(formData, buttonElement);
            }
        }
    }
}

// Función auxiliar para enviar la solicitud de aceptación
function enviarSolicitudAceptacion(formData, buttonElement) {
    // Obtener el serial del material antes de procesar
    const row = buttonElement.closest('tr');
    const serialCell = row ? row.querySelector('td:first-child') : null;
    const serialNumber = serialCell ? serialCell.textContent.trim() : null;
    
    // Realiza la solicitud POST utilizando AJAX
    var request = new XMLHttpRequest();
    request.open('POST', 'GET_LOGIS_DIRECTA.php');
    
    request.onload = function() {
        // Procesa la respuesta del servidor
        if (request.status === 200) {
            // Mostrar mensaje de éxito usando la función de notificación en lugar de alert
            mostrarNotificacion('Material aceptado correctamente', 'success');
            
            // ACTUALIZAR CACHE de manera inteligente en lugar de limpiarlo completamente
            updateCacheAfterAcceptance(serialNumber);
            console.log('🔄 Cache actualizado inteligentemente para serial:', serialNumber);
            
            // ELIMINAR LA FILA INMEDIATAMENTE de recepción con animación
            if (row && serialNumber) {
                console.log('🗑️ Eliminando fila de recepción inmediatamente:', serialNumber);
                
                // Usar la función centralizada para remover con animación
                removeRowWithAnimation(row, () => {
                    // Mostrar mensaje informativo
                    const recepcionTableBody = document.getElementById('recepcionTableBody');
                    if (recepcionTableBody && recepcionTableBody.children.length === 0) {
                        recepcionTableBody.innerHTML = `
                            <tr>
                                <td colspan="3" class="text-center text-muted">
                                    <i class="bi bi-inbox me-2"></i>
                                    No hay materiales pendientes de recepción
                                </td>
                            </tr>
                        `;
                    }
                });
            }
            
        } else {
            console.error('❌ Error en solicitud:', request.status, request.statusText);
            mostrarNotificacion('Error al procesar la solicitud. Código: ' + request.status, 'danger');
        }
    };
    
    request.onerror = function() {
        console.error('❌ Error de red en solicitud');
        mostrarNotificacion('Error de conexión. Verifique su red e intente nuevamente.', 'danger');
    };
    
    request.send(formData);
}

// Función para transferir registro
function transferirRegistro(Serial, ticket, accion, id_tecnico_destino, motivo) {
    // Mostrar indicador de carga en el botón
    const submitButton = document.querySelector('.submit-offcanvas-btn');
    if (submitButton) {
        addButtonSpinner(submitButton, 'Enviando...');
    }

    // Preparar datos para envío
    const postData = `Serial=${encodeURIComponent(Serial)}&ticket=${encodeURIComponent(ticket)}&accion=${encodeURIComponent(accion)}&id_tecnico_destino=${encodeURIComponent(id_tecnico_destino)}&id_tecnico_origen=${encodeURIComponent(window.userId)}&motivo=${encodeURIComponent(motivo)}`;

    return new Promise((resolve, reject) => {
        const request = new XMLHttpRequest();
        request.timeout = 30000;

        request.onreadystatechange = function() {
            if (request.readyState === XMLHttpRequest.DONE) {
                if (request.status === 200) {
                    try {
                        const response = JSON.parse(request.responseText);
                        if (response.success) {
                            mostrarNotificacion('Transferencia realizada exitosamente', 'success');
                            
                            // Invalidar cache para forzar recarga
                            invalidateTableCache('directa');
                            
                            // Cerrar offcanvas
                            const offcanvasElement = document.getElementById('offcanvasrigh');
                            if (offcanvasElement) {
                                const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvasElement);
                                if (offcanvasInstance) {
                                    offcanvasInstance.hide();
                                }
                            }
                            
                            resolve(response);
                        } else {
                            reject(new Error(response.message || 'Error en la transferencia'));
                        }
                    } catch (e) {
                        reject(new Error('Error parseando respuesta del servidor'));
                    }
                } else {
                    reject(new Error('Error de conexión con el servidor'));
                }
                
                // Remover spinner del botón
                if (submitButton) {
                    removeButtonSpinner(submitButton, 'Transferir');
                }
            }
        };

        request.ontimeout = function() {
            if (submitButton) {
                removeButtonSpinner(submitButton, 'Transferir');
            }
            reject(new Error('Tiempo de espera agotado'));
        };

        request.open('POST', 'GET_LOGIS_DIRECTA.php');
        request.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        request.send(postData);
    });
}

// Función para mostrar modal de rechazo
function rechazoMaterial(Serial, ticket, id_tecnico_destino, accion) {
    console.log('🚨 [HANDLERS] Iniciando rechazo de material:', { Serial, ticket, id_tecnico_destino, accion });

    const modal = document.getElementById('popup-container');
    console.log('🔍 [HANDLERS] Modal element found:', !!modal);

    if (modal) {
        console.log('✅ [HANDLERS] Modal encontrado, intentando mostrar...');

        // Configurar campos ocultos ANTES de mostrar el modal
        const serialField = document.getElementById('popupSerial');
        const ticketField = document.getElementById('popupTicket');
        const tecnicoField = document.getElementById('popupIdTecnicoDestino');
        const accionField = document.getElementById('popupAccion');

        if (serialField) {
            serialField.value = Serial;
            console.log('✅ [HANDLERS] Serial configurado:', Serial);
        }
        if (ticketField) {
            ticketField.value = ticket;
            console.log('✅ [HANDLERS] Ticket configurado:', ticket);
        }
        if (tecnicoField) {
            tecnicoField.value = id_tecnico_destino;
            console.log('✅ [HANDLERS] Técnico destino configurado:', id_tecnico_destino);
        }
        if (accionField) {
            accionField.value = accion;
            console.log('✅ [HANDLERS] Acción configurada:', accion);
        }

        // Limpiar el campo de motivo
        const motivoField = document.getElementById('motivoRechazo');
        if (motivoField) {
            motivoField.value = '';
        }

        // Mostrar el modal usando flex para que coincida con el CSS
        modal.style.display = 'flex';
        modal.classList.add('show');

        // Asegurar que los event listeners estén configurados cada vez que se abre el modal
        setupRechazoModalEvents();

        // Agregar event listener para cerrar modal al hacer clic fuera
        setupModalClickOutside();

        // Agregar event listener para cerrar modal con tecla Escape
        setupModalEscapeKey();

        console.log('✅ [HANDLERS] Modal de rechazo mostrado');
        console.log('🔍 [HANDLERS] Modal style after:', modal.style.display);
        console.log('🔍 [HANDLERS] Modal classes after:', modal.classList.toString());
    } else {
        console.error('❌ [HANDLERS] Modal popup-container no encontrado');
        console.log('🔍 [HANDLERS] Elementos con popup en ID:', document.querySelectorAll('[id*="popup"]'));
        console.log('🔍 [HANDLERS] Elementos con modal en class:', document.querySelectorAll('[class*="modal"]'));
    }
}

// Variables para evitar múltiples envíos
let rechazandoMaterial = false;
let ultimoRechazoTimestamp = 0;
let ultimoRechazoSerial = '';

// Función para aceptar rechazo
function Rechazoaceptar() {
    console.log('🔄 Procesando aceptación de rechazo...');

    // Obtener timestamp actual
    const ahora = Date.now();

    // Evitar múltiples envíos con múltiples verificaciones
    if (rechazandoMaterial) {
        console.warn('⚠️ Rechazo ya en proceso, ignorando clic adicional');
        return;
    }

    // Verificar si es un clic duplicado muy rápido (menos de 2 segundos)
    if (ahora - ultimoRechazoTimestamp < 2000) {
        console.warn('⚠️ Clic muy rápido detectado, ignorando para evitar duplicados');
        return;
    }
    
    const motivoField = document.getElementById('motivoRechazo');
    const serialField = document.getElementById('popupSerial');
    const ticketField = document.getElementById('popupTicket');
    const tecnicoField = document.getElementById('popupIdTecnicoDestino');
    const accionField = document.getElementById('popupAccion');

    if (!motivoField || !motivoField.value.trim()) {
        mostrarNotificacion('Debe ingresar un motivo para el rechazo', 'warning');
        return;
    }

    // Verificar si es el mismo serial que el último rechazo
    const serialActual = serialField?.value || '';
    if (serialActual === ultimoRechazoSerial && ahora - ultimoRechazoTimestamp < 5000) {
        console.warn('⚠️ Intento de rechazar el mismo serial muy rápido, ignorando');
        return;
    }

    // Marcar como en proceso y actualizar variables de control
    rechazandoMaterial = true;
    ultimoRechazoTimestamp = ahora;
    ultimoRechazoSerial = serialActual;
    const confirmButton = document.getElementById('btnConfirmarRechazo');
    if (confirmButton) {
        confirmButton.disabled = true;
        confirmButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Procesando...';
    }
    
    console.log('📋 Datos del rechazo:', {
        serial: serialField?.value,
        ticket: ticketField?.value,
        tecnico: tecnicoField?.value,
        accion: accionField?.value,
        motivo: motivoField.value
    });
    
    // Generar ID único para esta solicitud
    const requestId = `rechazo_${serialActual}_${ahora}_${Math.random().toString(36).substr(2, 9)}`;

    const formData = new FormData();
    formData.append('Serial', serialField.value);
    formData.append('ticket', ticketField.value);
    formData.append('id_tecnico_destino', tecnicoField.value);
    formData.append('accion', 'RECHAZA');
    formData.append('motivo', motivoField.value);
    formData.append('id_tecnico_origen', window.userId || window.ModLogisticaConfig?.user?.id);
    formData.append('request_id', requestId); // ID único para prevenir duplicados en servidor

    console.log('📋 Request ID generado:', requestId);
    
    // Enviar solicitud de rechazo usando AJAX estándar
    const request = new XMLHttpRequest();
    request.open('POST', 'GET_LOGIS_DIRECTA.php');
    
    request.onload = function() {
        if (request.status === 200) {
            console.log('✅ Rechazo procesado correctamente');
            
            // OBTENER EL SERIAL ANTES DE CERRAR EL MODAL
            const serialRechazado = serialField ? serialField.value : '';
            console.log('🔍 Serial para eliminar:', serialRechazado);
            
            mostrarNotificacion('Material rechazado correctamente', 'success');
            Rechazocancelar(); // Cerrar modal
            
            // REMOVER INMEDIATAMENTE la fila de la tabla de recepción
            console.log('🗑️ Eliminando fila rechazada de recepción:', serialRechazado);
            
            // Buscar y eliminar la fila específica
            const recepcionTbody = document.getElementById('recepcionTableBody');
            if (recepcionTbody) {
                console.log('🔍 Buscando fila en tbody:', recepcionTbody);
                const rows = recepcionTbody.querySelectorAll('tr');
                console.log('🔍 Total de filas encontradas:', rows.length);
                let filaEliminada = false;
                
                rows.forEach((row, index) => {
                    const serialCell = row.querySelector('td:nth-child(1)'); // PRIMERA COLUMNA es el serial
                    const serialEncontrado = serialCell ? serialCell.textContent.trim() : 'sin-serial';
                    console.log(`🔍 Fila ${index}: Serial = "${serialEncontrado}", Buscando = "${serialRechazado}"`);
                    
                    if (serialCell && serialCell.textContent.trim() === serialRechazado) {
                        console.log('✅ Fila encontrada y eliminando:', serialRechazado);
                        
                        // Animación de eliminación
                        row.style.transition = 'all 0.3s ease';
                        row.style.backgroundColor = '#f8d7da'; // Rojo claro
                        row.style.opacity = '0.5';
                        
                        setTimeout(() => {
                            // Simplificar: eliminar directamente sin función externa
                            row.remove();
                            console.log('🗑️ Fila eliminada directamente');
                            
                            // Verificar si la tabla quedó vacía
                            const remainingRows = recepcionTbody.querySelectorAll('tr:not(.loading-row)');
                            if (remainingRows.length === 0) {
                                recepcionTbody.innerHTML = `
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">
                                            <i class="bi bi-inbox me-2"></i>
                                            No hay materiales pendientes de recepción
                                        </td>
                                    </tr>
                                `;
                            }
                        }, 300);
                        
                        filaEliminada = true;
                    }
                });
                
                if (!filaEliminada) {
                    console.warn('⚠️ No se encontró la fila con serial:', serialRechazado);
                    console.log('🔍 Seriales encontrados:');
                    rows.forEach((row, index) => {
                        const serialCell = row.querySelector('td:nth-child(2)');
                        if (serialCell) {
                            console.log(`  Fila ${index}: "${serialCell.textContent.trim()}"`);
                        }
                    });
                }
            } else {
                console.error('❌ No se encontró el tbody de recepción');
            }
            
            // Invalidar cache de recepción para evitar que reaparezca
            if (window.ModLogisticaConfig && window.ModLogisticaConfig.tableCache) {
                window.ModLogisticaConfig.tableCache.recepcion = null;
                console.log('🔄 Cache de recepción invalidado');
            }
            
            // Restablecer estado al finalizar exitosamente
            rechazandoMaterial = false;
            
        } else {
            console.error('❌ Error en solicitud de rechazo:', request.status);
            mostrarNotificacion('Error al rechazar material. Código: ' + request.status, 'danger');
            
            // Restablecer estado en caso de error HTTP
            rechazandoMaterial = false;
            const confirmButton = document.getElementById('btnConfirmarRechazo');
            if (confirmButton) {
                confirmButton.disabled = false;
                confirmButton.innerHTML = '<i class="bi bi-check-circle me-2"></i>Confirmar Rechazo';
            }
        }
    };
    
    request.onerror = function() {
        console.error('❌ Error de red en rechazo');
        mostrarNotificacion('Error de conexión. Verifique su red e intente nuevamente.', 'danger');
        
        // Restablecer estado en caso de error
        rechazandoMaterial = false;
        const confirmButton = document.getElementById('btnConfirmarRechazo');
        if (confirmButton) {
            confirmButton.disabled = false;
            confirmButton.innerHTML = '<i class="bi bi-check-circle me-2"></i>Confirmar Rechazo';
        }
    };
    
    request.send(formData);
}

// Función para cancelar rechazo
function Rechazocancelar() {
    console.log('❌ Cancelando rechazo de material');
    
    // Restablecer estado de procesamiento
    rechazandoMaterial = false;
    const confirmButton = document.getElementById('btnConfirmarRechazo');
    if (confirmButton) {
        confirmButton.disabled = false;
        confirmButton.innerHTML = '<i class="bi bi-check-circle me-2"></i>Confirmar Rechazo';
    }
    
    const modal = document.getElementById('popup-container');
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        
        // Limpiar campos
        const motivoField = document.getElementById('motivoRechazo');
        if (motivoField) motivoField.value = '';
        
        // Limpiar campos ocultos
        const serialField = document.getElementById('popupSerial');
        const ticketField = document.getElementById('popupTicket');
        const tecnicoField = document.getElementById('popupIdTecnicoDestino');
        const accionField = document.getElementById('popupAccion');
        
        if (serialField) serialField.value = '';
        if (ticketField) ticketField.value = '';
        if (tecnicoField) tecnicoField.value = '';
        if (accionField) accionField.value = '';
        
        console.log('✅ Modal cerrado y campos limpiados');
    } else {
        console.error('❌ Modal popup-container no encontrado para cerrar');
    }
}

// Manejadores de eventos de offcanvas
function handleOffcanvasTransferirTecnicoClick() {
    toggleElementTransferencia('tecnicoTransf', true);
}

function handleOffcanvasJustificarClick() {
    toggleElementTransferencia('motivoASuper', true);
}

function handleOffcanvasBodegaSistemicoClick() {
    toggleElementTransferencia('motivo_tran_contain', true);
}

function handleOffcanvasBodegaSeriIncorrectaClick() {
    toggleElementTransferencia('serie_tran_contain', true);
}

function handleOffcanvasBodegaLinkClick() {
    toggleElementTransferencia('divArchivo', true);
}

function handleOffcanvasBodegaTOAClick() {
    toggleElementTransferencia('divArchivo', true);
}

function handleOffcanvasFotoCierreInvClick() {
    toggleElementTransferencia('divArchivo', true);
}

function handleOffcanvasDevueltoBodegaClick() {
    toggleElementTransferencia('divArchivo', true);
}

// Función auxiliar para remover fila por serial
function removeRowBySerial(tbodyId, serial) {
    const tbody = document.getElementById(tbodyId);
    if (!tbody) {
        console.warn('⚠️ Tbody no encontrado:', tbodyId);
        return;
    }
    
    const rows = tbody.querySelectorAll('tr');
    rows.forEach(row => {
        const serialCell = row.querySelector('td:nth-child(1)'); // PRIMERA columna contiene la serie
        if (serialCell && serialCell.textContent.trim() === serial) {
            console.log('🗑️ Removiendo fila con serial:', serial);
            removeRowWithAnimation(row, () => {
                // Verificar si quedan filas después de remover
                if (tbody.children.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="6" class="text-center text-muted">
                                <i class="bi bi-inbox me-2"></i>
                                No hay materiales pendientes de recepción
                            </td>
                        </tr>
                    `;
                }
            });
        }
    });
}

// Función auxiliar para actualizar cache después de aceptación
function updateCacheAfterAcceptance(serialNumber) {
    const tableCache = window.ModLogisticaConfig.tableCache;
    
    if (tableCache.recepcion && Array.isArray(tableCache.recepcion)) {
        // Remover el elemento de recepción
        tableCache.recepcion = tableCache.recepcion.filter(item => {
            const serial = item.Serial || item.serie;
            return serial !== serialNumber;
        });
        console.log('🔄 Serial removido del cache de recepción:', serialNumber);
    }
    
    // Invalidar cache de directa para que se recargue con el nuevo material
    if (tableCache.directa) {
        tableCache.directa = null;
        window.ModLogisticaConfig.loadingState.directa = false;
        console.log('🔄 Cache de directa invalidado para recarga');
    }
}

// Función para declarar instalación
function declararInstalacion(serie, idMovimiento) {
    const offcanvasElement = document.getElementById('offcanvasInstala');
    const serieInput = document.getElementById('serie_insta');
    
    if (offcanvasElement && serieInput) {
        serieInput.value = serie;
        const instalarOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
        instalarOffcanvas.show();
    }
}

// Función para configurar todos los event listeners
function setupEventListeners() {
    // Event listeners del documento
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔧 Configurando event listeners del módulo de logística');
        
        // Configurar botones de offcanvas si existen
        setupOffcanvasButtons();
        
        // Configurar formularios
        setupFormHandlers();
        
        // Configurar sistema de limpieza de offcanvas
        setupOffcanvasCleanup();
        
        // Configurar eventos del modal de rechazo
        setupRechazoModalEvents();
    });
}

// Función para configurar botones de offcanvas
function setupOffcanvasButtons() {
    // Manejadores específicos para cada tipo de offcanvas
    const offcanvasHandlers = {
        'handleOffcanvasTransferirTecnicoClick': handleOffcanvasTransferirTecnicoClick,
        'handleOffcanvasJustificarClick': handleOffcanvasJustificarClick,
        'handleOffcanvasBodegaSistemicoClick': handleOffcanvasBodegaSistemicoClick,
        'handleOffcanvasBodegaSeriIncorrectaClick': handleOffcanvasBodegaSeriIncorrectaClick,
        'handleOffcanvasBodegaLinkClick': handleOffcanvasBodegaLinkClick,
        'handleOffcanvasBodegaTOAClick': handleOffcanvasBodegaTOAClick,
        'handleOffcanvasFotoCierreInvClick': handleOffcanvasFotoCierreInvClick,
        'handleOffcanvasDevueltoBodegaClick': handleOffcanvasDevueltoBodegaClick
    };
    
    // Configurar handlers para elementos que existan
    Object.keys(offcanvasHandlers).forEach(handlerName => {
        const elements = document.querySelectorAll(`[onclick*="${handlerName}"]`);
        elements.forEach(element => {
            element.addEventListener('click', offcanvasHandlers[handlerName]);
        });
    });
}

// Función para configurar manejadores de formularios
function setupFormHandlers() {
    // Configurar formulario de transferencia
    const transferForm = document.getElementById('transferForm');
    if (transferForm) {
        transferForm.addEventListener('submit', handleTransferSubmit);
    }
    
    // Configurar formulario de instalación
    const installForm = document.getElementById('installForm');
    if (installForm) {
        installForm.addEventListener('submit', handleInstallSubmit);
    }
}

// Función para configurar limpieza de offcanvas
function setupOffcanvasCleanup() {
    document.body.addEventListener('hidden.bs.offcanvas', function() {
        removeModalOpenClass();
    });
}

// Configurar eventos del modal de rechazo
function setupRechazoModalEvents() {
    const btnConfirmar = document.getElementById('btnConfirmarRechazo');
    const btnCancelar = document.getElementById('btnCancelarRechazo');

    if (btnConfirmar) {
        // Remover event listener anterior si existe
        btnConfirmar.removeEventListener('click', Rechazoaceptar);
        // Agregar nuevo event listener
        btnConfirmar.addEventListener('click', Rechazoaceptar);
        console.log('✅ Event listener agregado a botón confirmar rechazo');
    } else {
        console.warn('⚠️ Botón confirmar rechazo no encontrado');
    }

    if (btnCancelar) {
        // Remover event listener anterior si existe
        btnCancelar.removeEventListener('click', Rechazocancelar);
        // Agregar nuevo event listener
        btnCancelar.addEventListener('click', Rechazocancelar);
        console.log('✅ Event listener agregado a botón cancelar rechazo');
    } else {
        console.warn('⚠️ Botón cancelar rechazo no encontrado');
    }
}

// Configurar evento para cerrar modal al hacer clic fuera
function setupModalClickOutside() {
    const modal = document.getElementById('popup-container');
    if (modal) {
        // Remover event listener anterior si existe
        modal.removeEventListener('click', handleModalClickOutside);
        // Agregar nuevo event listener
        modal.addEventListener('click', handleModalClickOutside);
        console.log('✅ Event listener agregado para cerrar modal al hacer clic fuera');
    }
}

// Manejar clic fuera del modal
function handleModalClickOutside(event) {
    const modalContent = document.querySelector('.modal-rechazo-content');
    if (event.target === event.currentTarget && !modalContent.contains(event.target)) {
        console.log('🔄 Cerrando modal por clic fuera');
        Rechazocancelar();
    }
}

// Configurar evento para cerrar modal con tecla Escape
function setupModalEscapeKey() {
    // Remover event listener anterior si existe
    document.removeEventListener('keydown', handleModalEscapeKey);
    // Agregar nuevo event listener
    document.addEventListener('keydown', handleModalEscapeKey);
    console.log('✅ Event listener agregado para cerrar modal con Escape');
}

// Manejar tecla Escape para cerrar modal
function handleModalEscapeKey(event) {
    if (event.key === 'Escape') {
        const modal = document.getElementById('popup-container');
        if (modal && window.getComputedStyle(modal).display !== 'none') {
            console.log('🔄 Cerrando modal con tecla Escape');
            Rechazocancelar();
        }
    }
}

// Manejador de envío de formulario de transferencia
function handleTransferSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const serial = formData.get('serie');
    const motivo = formData.get('motivo');
    const tecnicoDestino = formData.get('tecnico_destino');
    
    if (!serial || !motivo || !tecnicoDestino) {
        mostrarNotificacion('Todos los campos son obligatorios', 'warning');
        return;
    }
    
    transferirRegistro(serial, '', 'TRANSFIERE', tecnicoDestino, motivo)
        .then(() => {
            e.target.reset();
        })
        .catch(error => {
            mostrarNotificacion('Error en transferencia: ' + error.message, 'danger');
        });
}

// Manejador de envío de formulario de instalación
function handleInstallSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    declararInstalacion(formData)
        .then(() => {
            mostrarNotificacion('Instalación declarada correctamente', 'success');
            e.target.reset();
        })
        .catch(error => {
            mostrarNotificacion('Error al declarar instalación: ' + error.message, 'danger');
        });
}

// Exportar funciones críticas al scope global INMEDIATAMENTE para evitar sobrescrituras
window.rechazoMaterial = rechazoMaterial;
window.Rechazoaceptar = Rechazoaceptar;
window.Rechazocancelar = Rechazocancelar;
window.actualizarRegistro = actualizarRegistro;
window.redirigirEnTransferencia = redirigirEnTransferencia;
window.transferirRegistro = transferirRegistro;
window.declararInstalacion = declararInstalacion;
window.enviarSolicitudAceptacion = enviarSolicitudAceptacion;
window.updateCacheAfterAcceptance = updateCacheAfterAcceptance;

// Exportar funciones al scope global para compatibilidad
window.ModLogisticaHandlers = {
    redirigirEnTransferencia,
    actualizarRegistro,
    enviarSolicitudAceptacion,
    transferirRegistro,
    rechazoMaterial,
    Rechazoaceptar,
    Rechazocancelar,
    declararInstalacion,
    setupEventListeners,
    updateCacheAfterAcceptance
};

// Inicializar event listeners cuando el DOM esté listo
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupEventListeners);
} else {
    // DOM ya está listo
    setupEventListeners();
}

// Log de inicialización
if (window.ModLogisticaConfig && window.ModLogisticaConfig.debug) {
    console.log('🎮 Handlers.js cargado - Manejadores de eventos inicializados');
    console.log('🔍 Función rechazoMaterial disponible:', typeof window.rechazoMaterial);
}