/**
 * ajax-handlers.js
 * Gestión de comunicaciones AJAX para el módulo de logística
 * 
 * Contiene todas las funciones para envío de formularios,
 * comunicación con el servidor y manejo de respuestas.
 */

// Funciones AJAX para envío de formularios
function enviarTransferencia() {
    ModLogistica.debugLog('Enviando transferencia');

    // Validar formulario antes del envío
    if (!validarFormulario('transferencia')) {
        return;
    }

    const serie = document.getElementById('serie_tran').value;
    const usuarioDestino = document.getElementById('usuario_destino').value;
    const motivo = document.getElementById('motivo_tran').value;
    const serieNueva = document.getElementById('serie_tran_new').value;
    const motivoSelect = document.getElementById('defaultSelectSm').value;

    // Validar que usuarioDestino sea un número válido
    const usuarioDestinoNum = parseInt(usuarioDestino);
    if (isNaN(usuarioDestinoNum) || usuarioDestinoNum <= 0) {
        alert('Por favor selecciona un técnico destino válido');
        console.error('❌ Usuario destino inválido:', usuarioDestino);
        return;
    }

    console.log('🔍 Datos de transferencia:', {
        serie: serie,
        usuarioDestino: usuarioDestino,
        usuarioDestinoNum: usuarioDestinoNum,
        motivo: motivo
    });

    const formData = new FormData();
    formData.append('accion', 'transfiere');
    formData.append('serie', serie);
    formData.append('id_tecnico_origen', window.userId || window.ModLogisticaConfig?.user?.id || '');
    formData.append('id_tecnico_destino', usuarioDestinoNum.toString());
    formData.append('motivo', motivo);
    formData.append('serie_nueva', serieNueva);
    formData.append('motivo_select', motivoSelect);

    // Agregar archivo si existe
    const fileInput = document.getElementById('fileInventario');
    if (fileInput && fileInput.files.length > 0) {
        formData.append('fileInventario', fileInput.files[0]);
    }

    enviarFormularioAjaxMejorado(
        formData, 
        'Transferencia enviada correctamente', 
        'offcanvasrigh', 
        'transferButton', 
        'Enviar requerimiento'
    );
}

function enviarInstalacion() {
    ModLogistica.debugLog('Enviando instalación');
    
    // Validar formulario antes del envío
    if (!validarFormulario('instalacion')) {
        return;
    }

    const serie = document.getElementById('serie_insta').value;
    const ordenTrabajo = document.getElementById('formOT_insta').value;
    const rutCliente = document.getElementById('rut_insta').value;
    const observaciones = document.getElementById('obs_insta').value;

    // Debug: Log de los datos que se van a enviar
    console.log('📋 Datos de instalación:', {
        serie: serie,
        ordenTrabajo: ordenTrabajo,
        rutCliente: rutCliente,
        observaciones: observaciones
    });

    const formData = new FormData();
    formData.append('accion', 'instala');
    formData.append('Serial', serie);
    formData.append('rut_instalado', rutCliente);
    formData.append('OT_instala', ordenTrabajo);
    formData.append('observacion', observaciones);
    formData.append('id_tecnico_origen', window.userId || '');
    formData.append('id_tecnico_destino', window.userId || '');
    formData.append('motivo', 'Instalación declarada por técnico');
    formData.append('ticket', ordenTrabajo);

    // Agregar archivo si existe
    const fileInput = document.getElementById('fileInsta');
    if (fileInput && fileInput.files.length > 0) {
        formData.append('fileInsta', fileInput.files[0]);
        console.log('📎 Archivo adjunto:', fileInput.files[0].name);
    }

    enviarFormularioAjaxMejorado(
        formData, 
        'Instalación declarada correctamente', 
        'offcanvasInstala', 
        'instalButton', 
        'Declarar instalada'
    );
}

function enviarTransferenciaReversa() {
    ModLogistica.debugLog('Enviando transferencia reversa');
    
    // Validar formulario antes del envío
    if (!validarFormulario('transferencia_reversa')) {
        return;
    }

    const serie = document.getElementById('serie_trans_rever').value;
    const rutReversa = document.getElementById('rutReversa').value;
    const ordenReversa = document.getElementById('ordenReversa').value;
    const superDestino = document.getElementById('super_destino').value;
    const motivoReversa = document.getElementById('listReversa').value;
    const serieNuevaReversa = document.getElementById('serieNewReversa').value;
    const observaciones = document.getElementById('obs_rev_tra').value;

    const formData = new FormData();
    formData.append('accion', 'transferencia_reversa');
    formData.append('serie', serie);
    formData.append('rut_reversa', rutReversa);
    formData.append('orden_reversa', ordenReversa);
    formData.append('super_destino', superDestino);
    formData.append('motivo_reversa', motivoReversa);
    formData.append('serie_nueva_reversa', serieNuevaReversa);
    formData.append('observaciones', observaciones);

    // Agregar archivo si existe
    const fileInput = document.getElementById('userfile');
    if (fileInput && fileInput.files.length > 0) {
        formData.append('archivo', fileInput.files[0]);
    }

    enviarFormularioAjaxMejorado(
        formData, 
        'Transferencia reversa enviada correctamente', 
        'offcanvasrevSuper', 
        'transferReversaButton', 
        'Solicitar requerimiento'
    );
}

function enviarDeclaracionReversa() {
    ModLogistica.debugLog('Enviando declaración reversa');
    
    // Validar formulario antes del envío
    if (!validarFormulario('declaracion_reversa')) {
        return;
    }

    const serie = document.getElementById('serieReversaDeclara').value;

    const formData = new FormData();
    formData.append('accion', 'declaracion_reversa');
    formData.append('serie', serie);

    // Agregar archivo si existe
    const fileInput = document.getElementById('fileReversaDecla');
    if (fileInput && fileInput.files.length > 0) {
        formData.append('archivo', fileInput.files[0]);
    }

    enviarFormularioAjaxMejorado(
        formData, 
        'Declaración de entrega enviada correctamente', 
        'offcanvasReversaDeclara', 
        'reversaDeclaraButton', 
        'Declarar entrega'
    );
}

// Función genérica para enviar formularios via AJAX (versión básica)
function enviarFormularioAjax(formData, mensajeExito, offcanvasId) {
    const xhr = new XMLHttpRequest();
    xhr.open('POST', window.ModLogisticaConfig.endpoints.main, true);

    xhr.onload = function() {
        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                    alert(mensajeExito);
                    // Cerrar offcanvas
                    ocultarOffcanvas(offcanvasId);
                    // Recargar las tablas para mostrar cambios
                    location.reload();
                } else {
                    alert('Error: ' + (response.message || 'Error desconocido'));
                }
            } catch (e) {
                ModLogistica.errorLog('Error al procesar respuesta:', e);
                alert('Error al procesar la respuesta del servidor');
            }
        } else {
            alert('Error de comunicación con el servidor');
        }
    };

    xhr.onerror = function() {
        alert('Error de conexión');
    };

    xhr.send(formData);
}

// Función mejorada para envío de formularios con indicadores de carga
function enviarFormularioAjaxMejorado(formData, mensajeExito, offcanvasId, buttonId, textoBoton) {
    // Usar función de UI si está disponible, sino usar fallback
    if (typeof mostrarCargandoBoton === 'function') {
        mostrarCargandoBoton(buttonId, 'Enviando...');
    } else {
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = true;
            button.innerHTML = 'Enviando...';
        }
    }

    const xhr = new XMLHttpRequest();
    const endpoint = window.ModLogisticaConfig.endpoints.main;
    console.log('🎯 Enviando a endpoint:', endpoint);
    xhr.open('POST', endpoint, true);

    xhr.onload = function() {
        // Usar función de UI si está disponible, sino usar fallback
        if (typeof restaurarBoton === 'function') {
            restaurarBoton(buttonId, textoBoton);
        } else {
            const button = document.getElementById(buttonId);
            if (button) {
                button.disabled = false;
                button.innerHTML = textoBoton;
            }
        }

        if (xhr.status === 200) {
            try {
                // Debug: Log de la respuesta del servidor
                console.log('📤 Respuesta del servidor:', xhr.responseText.substring(0, 200));
                
                // Verificar si la respuesta es JSON válido
                if (xhr.responseText.trim().startsWith('<')) {
                    throw new Error('Servidor devolvió HTML en lugar de JSON. Posible error PHP.');
                }
                
                // Detectar respuestas de éxito en texto plano
                if (xhr.responseText.includes('Operación completada con éxito') || 
                    xhr.responseText.includes('éxito') ||
                    xhr.responseText.includes('correctamente')) {
                    console.log('✅ Operación exitosa detectada en respuesta de texto plano');
                    
                    // Mostrar notificación de éxito
                    if (typeof mostrarNotificacion === 'function') {
                        mostrarNotificacion(mensajeExito, 'success');
                    } else {
                        alert(mensajeExito);
                    }
                    
                    // Limpiar y cerrar formulario
                    if (typeof limpiarFormulario === 'function') {
                        limpiarFormulario(offcanvasId);
                    }
                    if (typeof resetearValidacionesFormulario === 'function') {
                        resetearValidacionesFormulario(offcanvasId);
                    }
                    if (typeof ocultarOffcanvas === 'function') {
                        ocultarOffcanvas(offcanvasId);
                    }
                    
                    // Las tablas se actualizarán automáticamente vía SSE
                    console.log('✅ Operación completada. Esperando actualización automática vía SSE...');
                    return;
                }
                
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                    // Mostrar notificación de éxito
                    if (typeof mostrarNotificacion === 'function') {
                        mostrarNotificacion(mensajeExito, 'success');
                    } else {
                        alert(mensajeExito);
                    }
                    
                    // Limpiar y cerrar formulario
                    if (typeof limpiarFormulario === 'function') {
                        limpiarFormulario(offcanvasId);
                    }
                    if (typeof resetearValidacionesFormulario === 'function') {
                        resetearValidacionesFormulario(offcanvasId);
                    }
                    if (typeof ocultarOffcanvas === 'function') {
                        ocultarOffcanvas(offcanvasId);
                    }
                    
                    // Las tablas se actualizarán automáticamente vía SSE
                    console.log('✅ Operación JSON completada. Esperando actualización automática vía SSE...');
                } else {
                    if (typeof mostrarNotificacion === 'function') {
                        mostrarNotificacion('Error: ' + (response.message || 'Error desconocido'), 'error');
                    } else {
                        alert('Error: ' + (response.message || 'Error desconocido'));
                    }
                }
            } catch (e) {
                ModLogistica.errorLog('Error al procesar respuesta:', e);
                console.error('🚨 Respuesta completa del servidor:', xhr.responseText);
                
                let errorMessage = 'Error al procesar la respuesta del servidor';
                if (xhr.responseText.includes('Fatal error')) {
                    errorMessage = 'Error fatal en el servidor PHP. Revise los logs del servidor.';
                } else if (xhr.responseText.includes('Parse error')) {
                    errorMessage = 'Error de sintaxis en el código PHP del servidor.';
                } else if (xhr.responseText.includes('Warning')) {
                    errorMessage = 'Advertencia en el servidor PHP. La operación puede no haberse completado.';
                }
                
                if (typeof mostrarNotificacion === 'function') {
                    mostrarNotificacion(errorMessage, 'error');
                } else {
                    alert(errorMessage);
                }
            }
        } else {
            if (typeof mostrarNotificacion === 'function') {
                mostrarNotificacion('Error de comunicación con el servidor', 'error');
            } else {
                alert('Error de comunicación con el servidor');
            }
        }
    };

    xhr.onerror = function() {
        if (typeof restaurarBoton === 'function') {
            restaurarBoton(buttonId, textoBoton);
        } else {
            const button = document.getElementById(buttonId);
            if (button) {
                button.disabled = false;
                button.innerHTML = textoBoton;
            }
        }
        if (typeof mostrarNotificacion === 'function') {
            mostrarNotificacion('Error de conexión', 'error');
        } else {
            alert('Error de conexión');
        }
    };

    xhr.send(formData);
}

// Función para cargar lista de técnicos
function cargarListaTecnicos() {
    const select = document.getElementById('usuario_destino');
    if (!select || select.options.length > 1) return; // Ya está cargado

    ModLogistica.debugLog('Cargando lista de técnicos');

    const xhr = new XMLHttpRequest();
    xhr.open('GET', window.ModLogisticaConfig.endpoints.tecnicos, true);
    xhr.onload = function() {
        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.success && response.tecnicos) {
                    response.tecnicos.forEach(function(tecnico) {
                        const option = document.createElement('option');
                        option.value = tecnico.id;
                        option.textContent = tecnico.nombre;
                        select.appendChild(option);
                    });
                    ModLogistica.debugLog('Lista de técnicos cargada:', response.tecnicos.length);
                }
            } catch (e) {
                ModLogistica.errorLog('Error al cargar lista de técnicos:', e);
            }
        }
    };
    xhr.send();
}

// Función para actualizar tablas dinámicamente después de acciones
function actualizarTabla(tipoTabla) {
    ModLogistica.debugLog('Actualizando tabla:', tipoTabla);
    
    const xhr = new XMLHttpRequest();
    xhr.open('GET', `${window.ModLogisticaConfig.endpoints.actualizarTabla}?accion=actualizar_tabla&tipo=${tipoTabla}`, true);
    xhr.onload = function() {
        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.success && response.html) {
                    const tableBody = document.getElementById(tipoTabla + 'TableBody');
                    if (tableBody) {
                        tableBody.innerHTML = response.html;
                        // Reinicializar event listeners para los nuevos elementos
                        inicializarEventListenersTabla();
                        ModLogistica.debugLog('Tabla actualizada:', tipoTabla);
                    }
                }
            } catch (e) {
                ModLogistica.errorLog('Error al actualizar tabla:', e);
            }
        }
    };
    xhr.send();
}

// Actualización: Archivo modificado para transferencia al servidor - Agregado debugging avanzado
console.log('✅ Ajax-handlers.js - Manejadores AJAX cargados correctamente con debugging avanzado');
